# MySQL to PostgreSQL 转换工具

这是一个用 Go 编写的 MySQL 到 PostgreSQL SQL 文件转换工具。

## 功能特性

- 自动转换 MySQL 数据类型到 PostgreSQL 兼容类型
- 处理字符集和排序规则
- 转换索引语法
- 移除 MySQL 特有的注释和设置
- 处理 AUTO_INCREMENT 到 SERIAL 的转换
- 支持大文件处理（增加了缓冲区大小）

## 使用方法

### 编译和运行

```bash
# 直接运行
go run mysql_to_pgsql_converter.go <输入文件> <输出文件>

# 或者先编译
go build mysql_to_pgsql_converter.go
./mysql_to_pgsql_converter <输入文件> <输出文件>
```

### 示例

```bash
go run mysql_to_pgsql_converter.go mishi_20250824153234n0ouy.sql converted_pgsql.sql
```

## 转换规则

### 数据类型转换

| MySQL | PostgreSQL |
|-------|------------|
| `int AUTO_INCREMENT` | `SERIAL` |
| `int NOT NULL` | `INTEGER NOT NULL` |
| `int` | `INTEGER` |
| `datetime` | `TIMESTAMP` |
| `text` | `TEXT` |

### 语法转换

- 反引号 (`) → 双引号 (")
- 移除 `CHARACTER SET` 和 `COLLATE` 子句
- 移除 `ENGINE` 和 `DEFAULT CHARSET` 设置
- 转换 `UNIQUE KEY` 为 `CONSTRAINT ... UNIQUE`
- 普通索引转换为单独的 `CREATE INDEX` 语句

### 移除的 MySQL 特有语法

- `/*!...*/` 注释
- `SET @...` 变量设置
- `SET NAMES`, `SET TIME_ZONE` 等设置
- `LOCK TABLES` 和 `UNLOCK TABLES`
- `ALTER TABLE ... DISABLE/ENABLE KEYS`

## 转换后的注意事项

1. **检查数据类型**：确保所有数据类型转换正确
2. **索引创建**：普通索引已转换为单独的 CREATE INDEX 语句
3. **测试数据**：建议先用小部分数据测试导入
4. **字符编码**：PostgreSQL 默认使用 UTF-8 编码

## 导入到 PostgreSQL

转换完成后，可以使用以下命令导入到 PostgreSQL：

```bash
# 连接到 PostgreSQL 并执行 SQL 文件
psql -U username -d database_name -f converted_pgsql.sql

# 或者使用管道
cat converted_pgsql.sql | psql -U username -d database_name
```

## 示例转换结果

### 原始 MySQL 表结构
```sql
CREATE TABLE `dog_words` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_content` (`content`(500)) USING BTREE,
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1551 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='舔狗日记';
```

### 转换后的 PostgreSQL 表结构
```sql
CREATE TABLE "dog_words" (
"id" SERIAL, -- 主键
"content" TEXT NOT NULL, -- 内容
"create_time" TIMESTAMP NOT NULL, -- 创建时间
"update_time" TIMESTAMP NOT NULL, -- 更新时间
PRIMARY KEY ("id"),
CONSTRAINT unique_content UNIQUE (content)
); -- 舔狗日记

-- Create index for create_time
CREATE INDEX idx_create_time ON "dog_words" ("create_time");
```

## 限制和已知问题

1. **复杂查询**：只处理基本的 DDL 和 DML 语句
2. **存储过程**：不支持 MySQL 存储过程转换
3. **触发器**：不支持触发器转换
4. **视图**：基本视图可以转换，复杂视图可能需要手动调整
5. **函数**：MySQL 特有函数需要手动替换为 PostgreSQL 等价函数

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个工具。

## 许可证

MIT License
